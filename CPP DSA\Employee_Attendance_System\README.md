# Employee Attendance System

A comprehensive C++ implementation of an Employee Attendance Management System using a **Doubly-Circular Linked List** data structure.

## Features

### Core Data Structure
- **Doubly-Circular Linked List**: Each node has pointers to both next and previous nodes, with the last node pointing back to the first node, creating a circular structure.

### Basic Operations
- **Add Employee**: Insert new employee records into the system
- **Remove Employee**: Delete employee records from the system
- **Display All**: Show all employees in the system

### Attendance Operations
- **Check-In**: Record employee arrival time with automatic timestamp
- **Check-Out**: Record employee departure time and calculate hours worked
- **Real-time Status**: Track which employees are currently checked in/out

### Search Operations
- **Search by ID**: Find employees using their unique employee ID
- **Search by Name**: Locate employees by their full name
- **Filter by Department**: Display all employees in a specific department

### Update Operations
- **Update Name**: Modify employee name information
- **Update Department**: Change employee department assignment

### Sorting Operations
- **Sort by ID**: Arrange employees in ascending order by employee ID
- **Sort by Name**: Alphabetically sort employees by name
- **Sort by Hours Worked**: Sort employees by total hours worked (descending)

### Analytics
- **Statistics Dashboard**: View comprehensive attendance statistics
- **Hours Calculation**: Automatic calculation of work hours
- **Department Analytics**: Track attendance by department

## File Structure

```
Employee_Attendance_System/
├── employee_attendance.h      # Header file with class declarations
├── employee_attendance.cpp    # Implementation of all functions
├── main.cpp                   # Main program with interactive menu
├── Makefile                   # Build configuration
└── README.md                  # This documentation file
```

## Data Structures

### Employee Structure
```cpp
struct Employee {
    int empId;              // Unique employee identifier
    string name;            // Employee full name
    string department;      // Department assignment
    Date checkInDate;       // Date of check-in
    Time checkInTime;       // Time of check-in
    Date checkOutDate;      // Date of check-out
    Time checkOutTime;      // Time of check-out
    bool isCheckedIn;       // Current status
    double hoursWorked;     // Total hours worked
};
```

### Node Structure
```cpp
struct Node {
    Employee data;          // Employee information
    Node* next;            // Pointer to next node
    Node* prev;            // Pointer to previous node
};
```

## Compilation and Execution

### Using Makefile (Recommended)
```bash
# Compile the program
make

# Run the program
make run

# Clean build files
make clean

# Build with debug information
make debug
```

### Manual Compilation
```bash
g++ -std=c++11 -Wall -Wextra -O2 main.cpp employee_attendance.cpp -o employee_attendance
./employee_attendance
```

## Usage

### Interactive Menu
The program provides an interactive menu with the following options:

1. **Add Employee** - Register new employees
2. **Remove Employee** - Delete employee records
3. **Check In Employee** - Record arrival time
4. **Check Out Employee** - Record departure time
5. **Search Employee by ID** - Find by employee ID
6. **Search Employee by Name** - Find by name
7. **Display All Employees** - Show complete list
8. **Display Checked-In Employees** - Show currently present employees
9. **Display Checked-Out Employees** - Show employees who have left
10. **Display Employees by Department** - Filter by department
11. **Update Employee Name** - Modify name
12. **Update Employee Department** - Change department
13. **Sort by ID** - Sort by employee ID
14. **Sort by Name** - Sort alphabetically
15. **Sort by Hours Worked** - Sort by work hours
16. **Display Statistics** - Show attendance analytics
17. **Run Demo Test** - Execute comprehensive test scenario

### Demo Test
Option 17 runs a comprehensive demonstration that:
- Adds sample employees
- Performs check-in/check-out operations
- Demonstrates search functionality
- Shows update operations
- Tests all sorting methods
- Displays final statistics

## Key Algorithms

### Doubly-Circular Linked List Operations
- **Insertion**: O(1) at head/tail, O(n) at arbitrary position
- **Deletion**: O(1) if node is known, O(n) for search + delete
- **Search**: O(n) linear search
- **Traversal**: O(n) for complete traversal

### Sorting Algorithms
- **Bubble Sort**: Used for sorting operations (O(n²) time complexity)
- Suitable for small to medium datasets
- In-place sorting with minimal memory overhead

## Time Complexity Analysis

| Operation | Time Complexity | Space Complexity |
|-----------|----------------|------------------|
| Add Employee | O(1) | O(1) |
| Remove Employee | O(n) | O(1) |
| Search by ID/Name | O(n) | O(1) |
| Check In/Out | O(n) | O(1) |
| Display Operations | O(n) | O(1) |
| Sorting | O(n²) | O(1) |

## Features Demonstrated

### Doubly-Circular Linked List Properties
1. **Bidirectional Traversal**: Can move forward and backward through the list
2. **Circular Nature**: Last node connects back to first node
3. **No NULL Pointers**: All nodes have valid next and previous pointers
4. **Efficient Insertion/Deletion**: At known positions

### Real-world Application
- **Attendance Tracking**: Realistic check-in/check-out system
- **Time Management**: Automatic calculation of work hours
- **Employee Management**: Complete CRUD operations
- **Reporting**: Statistical analysis and reporting features

## Testing

The program includes comprehensive testing through:
- Interactive menu for manual testing
- Automated demo test (Option 17)
- Error handling for edge cases
- Input validation

## Error Handling

- Duplicate employee ID prevention
- Invalid operation detection (e.g., checking out non-checked-in employee)
- Empty list handling
- Input validation and sanitization

## Future Enhancements

Potential improvements could include:
- Persistent data storage (file I/O)
- Advanced sorting algorithms (Quick Sort, Merge Sort)
- Date range filtering
- Export functionality (CSV, JSON)
- Multi-day attendance tracking
- Employee performance analytics
