#ifndef EMPLOYEE_ATTENDANCE_H
#define EMPLOYEE_ATTENDANCE_H

#include <iostream>
#include <string>
#include <ctime>
#include <iomanip>
#include <sstream>

using namespace std;

// Structure to represent time
struct Time {
    int hour;
    int minute;
    int second;
    
    Time() : hour(0), minute(0), second(0) {}
    Time(int h, int m, int s) : hour(h), minute(m), second(s) {}
    
    string toString() const {
        stringstream ss;
        ss << setfill('0') << setw(2) << hour << ":"
           << setfill('0') << setw(2) << minute << ":"
           << setfill('0') << setw(2) << second;
        return ss.str();
    }
};

// Structure to represent date
struct Date {
    int day;
    int month;
    int year;
    
    Date() : day(1), month(1), year(2024) {}
    Date(int d, int m, int y) : day(d), month(m), year(y) {}
    
    string toString() const {
        stringstream ss;
        ss << setfill('0') << setw(2) << day << "/"
           << setfill('0') << setw(2) << month << "/"
           << year;
        return ss.str();
    }
};

// Employee structure
struct Employee {
    int empId;
    string name;
    string department;
    Date checkInDate;
    Time checkInTime;
    Date checkOutDate;
    Time checkOutTime;
    bool isCheckedIn;
    double hoursWorked;
    
    Employee() : empId(0), name(""), department(""), isCheckedIn(false), hoursWorked(0.0) {}
    
    Employee(int id, string n, string dept) 
        : empId(id), name(n), department(dept), isCheckedIn(false), hoursWorked(0.0) {}
    
    void displayInfo() const {
        cout << "ID: " << empId << ", Name: " << name 
             << ", Department: " << department;
        if (isCheckedIn) {
            cout << " [CHECKED IN at " << checkInTime.toString() 
                 << " on " << checkInDate.toString() << "]";
        } else {
            cout << " [CHECKED OUT]";
            if (hoursWorked > 0) {
                cout << " - Hours worked: " << fixed << setprecision(2) << hoursWorked;
            }
        }
        cout << endl;
    }
};

// Node structure for doubly-circular linked list
struct Node {
    Employee data;
    Node* next;
    Node* prev;
    
    Node(const Employee& emp) : data(emp), next(nullptr), prev(nullptr) {}
};

// Doubly-Circular Linked List class for Employee Attendance
class EmployeeAttendanceSystem {
private:
    Node* head;
    int size;
    
    // Helper function to get current time
    Time getCurrentTime() {
        time_t now = time(0);
        tm* ltm = localtime(&now);
        return Time(ltm->tm_hour, ltm->tm_min, ltm->tm_sec);
    }
    
    // Helper function to get current date
    Date getCurrentDate() {
        time_t now = time(0);
        tm* ltm = localtime(&now);
        return Date(ltm->tm_mday, 1 + ltm->tm_mon, 1900 + ltm->tm_year);
    }
    
    // Helper function to calculate hours worked
    double calculateHours(const Time& checkIn, const Time& checkOut) {
        int totalMinutesIn = checkIn.hour * 60 + checkIn.minute;
        int totalMinutesOut = checkOut.hour * 60 + checkOut.minute;
        
        if (totalMinutesOut < totalMinutesIn) {
            totalMinutesOut += 24 * 60; // Next day
        }
        
        return (totalMinutesOut - totalMinutesIn) / 60.0;
    }

public:
    EmployeeAttendanceSystem() : head(nullptr), size(0) {}
    
    ~EmployeeAttendanceSystem() {
        clear();
    }
    
    // Basic operations
    void addEmployee(int id, const string& name, const string& department);
    bool removeEmployee(int id);
    void clear();
    bool isEmpty() const { return head == nullptr; }
    int getSize() const { return size; }
    
    // Attendance operations
    bool checkIn(int empId);
    bool checkOut(int empId);
    
    // Search operations
    Node* searchById(int id);
    Node* searchByName(const string& name);
    void displayEmployeesByDepartment(const string& department);
    
    // Update operations
    bool updateEmployeeName(int id, const string& newName);
    bool updateEmployeeDepartment(int id, const string& newDepartment);
    
    // Display operations
    void displayAll();
    void displayCheckedInEmployees();
    void displayCheckedOutEmployees();
    
    // Sorting operations
    void sortById();
    void sortByName();
    void sortByHoursWorked();
    
    // Utility functions
    void displayStatistics();
};

#endif
