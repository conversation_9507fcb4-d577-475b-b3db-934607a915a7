#include "employee_attendance.h"
#include <algorithm>

// Add employee to the doubly-circular linked list
void EmployeeAttendanceSystem::addEmployee(int id, const string &name, const string &department)
{
    // Check if employee already exists
    if (searchById(id) != nullptr)
    {
        cout << "Employee with ID " << id << " already exists!" << endl;
        return;
    }

    Employee newEmp(id, name, department);
    Node *newNode = new Node(newEmp);

    if (head == nullptr)
    {
        // First node
        head = newNode;
        newNode->next = newNode;
        newNode->prev = newNode;
    }
    else
    {
        // Insert at the end (before head)
        Node *tail = head->prev;

        newNode->next = head;
        newNode->prev = tail;
        tail->next = newNode;
        head->prev = newNode;
    }

    size++;
    cout << "Employee " << name << " (ID: " << id << ") added successfully!" << endl;
}

// Remove employee from the list
bool EmployeeAttendanceSystem::removeEmployee(int id)
{
    Node *nodeToDelete = searchById(id);
    if (nodeToDelete == nullptr)
    {
        cout << "Employee with ID " << id << " not found!" << endl;
        return false;
    }

    if (size == 1)
    {
        // Only one node
        head = nullptr;
    }
    else
    {
        // Update links
        nodeToDelete->prev->next = nodeToDelete->next;
        nodeToDelete->next->prev = nodeToDelete->prev;

        // Update head if necessary
        if (nodeToDelete == head)
        {
            head = nodeToDelete->next;
        }
    }

    cout << "Employee " << nodeToDelete->data.name << " (ID: " << id << ") removed successfully!" << endl;
    delete nodeToDelete;
    size--;
    return true;
}

// Clear all employees
void EmployeeAttendanceSystem::clear()
{
    if (head == nullptr)
        return;

    Node *current = head;
    do
    {
        Node *next = current->next;
        delete current;
        current = next;
    } while (current != head);

    head = nullptr;
    size = 0;
}

// Check-in operation
bool EmployeeAttendanceSystem::checkIn(int empId)
{
    Node *empNode = searchById(empId);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << empId << " not found!" << endl;
        return false;
    }

    if (empNode->data.isCheckedIn)
    {
        cout << "Employee " << empNode->data.name << " is already checked in!" << endl;
        return false;
    }

    empNode->data.isCheckedIn = true;
    empNode->data.checkInTime = getCurrentTime();
    empNode->data.checkInDate = getCurrentDate();

    cout << "Employee " << empNode->data.name << " checked in at "
         << empNode->data.checkInTime.toString() << " on "
         << empNode->data.checkInDate.toString() << endl;

    return true;
}

// Check-out operation
bool EmployeeAttendanceSystem::checkOut(int empId)
{
    Node *empNode = searchById(empId);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << empId << " not found!" << endl;
        return false;
    }

    if (!empNode->data.isCheckedIn)
    {
        cout << "Employee " << empNode->data.name << " is not checked in!" << endl;
        return false;
    }

    empNode->data.isCheckedIn = false;
    empNode->data.checkOutTime = getCurrentTime();
    empNode->data.checkOutDate = getCurrentDate();

    // Calculate hours worked
    empNode->data.hoursWorked = calculateHours(empNode->data.checkInTime, empNode->data.checkOutTime);

    cout << "Employee " << empNode->data.name << " checked out at "
         << empNode->data.checkOutTime.toString() << " on "
         << empNode->data.checkOutDate.toString()
         << ". Hours worked: " << fixed << setprecision(2) << empNode->data.hoursWorked << endl;

    return true;
}

// Search employee by ID
Node *EmployeeAttendanceSystem::searchById(int id)
{
    if (head == nullptr)
        return nullptr;

    Node *current = head;
    do
    {
        if (current->data.empId == id)
        {
            return current;
        }
        current = current->next;
    } while (current != head);

    return nullptr;
}

// Search employee by name
Node *EmployeeAttendanceSystem::searchByName(const string &name)
{
    if (head == nullptr)
        return nullptr;

    Node *current = head;
    do
    {
        if (current->data.name == name)
        {
            return current;
        }
        current = current->next;
    } while (current != head);

    return nullptr;
}

// Display employees by department
void EmployeeAttendanceSystem::displayEmployeesByDepartment(const string &department)
{
    if (head == nullptr)
    {
        cout << "No employees found!" << endl;
        return;
    }

    cout << "\n=== Employees in " << department << " Department ===" << endl;
    bool found = false;
    Node *current = head;
    do
    {
        if (current->data.department == department)
        {
            current->data.displayInfo();
            found = true;
        }
        current = current->next;
    } while (current != head);

    if (!found)
    {
        cout << "No employees found in " << department << " department." << endl;
    }
}

// Update employee name
bool EmployeeAttendanceSystem::updateEmployeeName(int id, const string &newName)
{
    Node *empNode = searchById(id);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << id << " not found!" << endl;
        return false;
    }

    string oldName = empNode->data.name;
    empNode->data.name = newName;
    cout << "Employee name updated from '" << oldName << "' to '" << newName << "'" << endl;
    return true;
}

// Update employee department
bool EmployeeAttendanceSystem::updateEmployeeDepartment(int id, const string &newDepartment)
{
    Node *empNode = searchById(id);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << id << " not found!" << endl;
        return false;
    }

    string oldDept = empNode->data.department;
    empNode->data.department = newDepartment;
    cout << "Employee department updated from '" << oldDept << "' to '" << newDepartment << "'" << endl;
    return true;
}

// Display all employees
void EmployeeAttendanceSystem::displayAll()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    cout << "\n=== All Employees ===" << endl;
    Node *current = head;
    do
    {
        current->data.displayInfo();
        current = current->next;
    } while (current != head);
    cout << "Total employees: " << size << endl;
}

// Display checked-in employees
void EmployeeAttendanceSystem::displayCheckedInEmployees()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    cout << "\n=== Checked-In Employees ===" << endl;
    bool found = false;
    Node *current = head;
    do
    {
        if (current->data.isCheckedIn)
        {
            current->data.displayInfo();
            found = true;
        }
        current = current->next;
    } while (current != head);

    if (!found)
    {
        cout << "No employees are currently checked in." << endl;
    }
}

// Display checked-out employees
void EmployeeAttendanceSystem::displayCheckedOutEmployees()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    cout << "\n=== Checked-Out Employees ===" << endl;
    bool found = false;
    Node *current = head;
    do
    {
        if (!current->data.isCheckedIn)
        {
            current->data.displayInfo();
            found = true;
        }
        current = current->next;
    } while (current != head);

    if (!found)
    {
        cout << "No employees are currently checked out." << endl;
    }
}

// Sort by ID (bubble sort)
void EmployeeAttendanceSystem::sortById()
{
    if (head == nullptr || size <= 1)
        return;

    bool swapped;
    do
    {
        swapped = false;
        Node *current = head;
        do
        {
            if (current->data.empId > current->next->data.empId)
            {
                // Swap employee data
                Employee temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        } while (current->next != head);
    } while (swapped);

    cout << "Employees sorted by ID." << endl;
}

// Sort by name (bubble sort)
void EmployeeAttendanceSystem::sortByName()
{
    if (head == nullptr || size <= 1)
        return;

    bool swapped;
    do
    {
        swapped = false;
        Node *current = head;
        do
        {
            if (current->data.name > current->next->data.name)
            {
                // Swap employee data
                Employee temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        } while (current->next != head);
    } while (swapped);

    cout << "Employees sorted by name." << endl;
}

// Sort by hours worked (bubble sort)
void EmployeeAttendanceSystem::sortByHoursWorked()
{
    if (head == nullptr || size <= 1)
        return;

    bool swapped;
    do
    {
        swapped = false;
        Node *current = head;
        do
        {
            if (current->data.hoursWorked < current->next->data.hoursWorked)
            {
                // Swap employee data (descending order)
                Employee temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        } while (current->next != head);
    } while (swapped);

    cout << "Employees sorted by hours worked (descending)." << endl;
}

// Display statistics
void EmployeeAttendanceSystem::displayStatistics()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    int checkedIn = 0, checkedOut = 0;
    double totalHours = 0.0;

    Node *current = head;
    do
    {
        if (current->data.isCheckedIn)
        {
            checkedIn++;
        }
        else
        {
            checkedOut++;
            totalHours += current->data.hoursWorked;
        }
        current = current->next;
    } while (current != head);

    cout << "\n=== Attendance Statistics ===" << endl;
    cout << "Total employees: " << size << endl;
    cout << "Currently checked in: " << checkedIn << endl;
    cout << "Currently checked out: " << checkedOut << endl;
    cout << "Total hours worked today: " << fixed << setprecision(2) << totalHours << endl;
    if (checkedOut > 0)
    {
        cout << "Average hours per employee: " << fixed << setprecision(2) << (totalHours / checkedOut) << endl;
    }
}
