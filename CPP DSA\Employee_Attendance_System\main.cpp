#include "employee_attendance.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace std;

void displayMenu() {
    cout << "\n========================================" << endl;
    cout << "   EMPLOYEE ATTENDANCE SYSTEM" << endl;
    cout << "========================================" << endl;
    cout << "1.  Add Employee" << endl;
    cout << "2.  Remove Employee" << endl;
    cout << "3.  Check In Employee" << endl;
    cout << "4.  Check Out Employee" << endl;
    cout << "5.  Search Employee by ID" << endl;
    cout << "6.  Search Employee by Name" << endl;
    cout << "7.  Display All Employees" << endl;
    cout << "8.  Display Checked-In Employees" << endl;
    cout << "9.  Display Checked-Out Employees" << endl;
    cout << "10. Display Employees by Department" << endl;
    cout << "11. Update Employee Name" << endl;
    cout << "12. Update Employee Department" << endl;
    cout << "13. Sort by ID" << endl;
    cout << "14. Sort by Name" << endl;
    cout << "15. Sort by Hours Worked" << endl;
    cout << "16. Display Statistics" << endl;
    cout << "17. Run Demo Test" << endl;
    cout << "0.  Exit" << endl;
    cout << "========================================" << endl;
    cout << "Enter your choice: ";
}

void runDemoTest(EmployeeAttendanceSystem& system) {
    cout << "\n=== RUNNING DEMO TEST ===" << endl;
    
    // Add sample employees
    cout << "\n--- Adding Sample Employees ---" << endl;
    system.addEmployee(101, "Alice Johnson", "IT");
    system.addEmployee(102, "Bob Smith", "HR");
    system.addEmployee(103, "Carol Davis", "IT");
    system.addEmployee(104, "David Wilson", "Finance");
    system.addEmployee(105, "Eva Brown", "HR");
    
    // Display all employees
    system.displayAll();
    
    // Check in some employees
    cout << "\n--- Check-in Operations ---" << endl;
    system.checkIn(101);
    this_thread::sleep_for(chrono::seconds(1));
    system.checkIn(103);
    this_thread::sleep_for(chrono::seconds(1));
    system.checkIn(105);
    
    // Display checked-in employees
    system.displayCheckedInEmployees();
    
    // Wait a bit and check out some employees
    cout << "\n--- Waiting 3 seconds to simulate work time ---" << endl;
    this_thread::sleep_for(chrono::seconds(3));
    
    cout << "\n--- Check-out Operations ---" << endl;
    system.checkOut(101);
    this_thread::sleep_for(chrono::seconds(1));
    system.checkOut(103);
    
    // Display checked-out employees
    system.displayCheckedOutEmployees();
    
    // Search operations
    cout << "\n--- Search Operations ---" << endl;
    Node* found = system.searchById(102);
    if (found) {
        cout << "Found employee by ID 102: ";
        found->data.displayInfo();
    }
    
    found = system.searchByName("Eva Brown");
    if (found) {
        cout << "Found employee by name 'Eva Brown': ";
        found->data.displayInfo();
    }
    
    // Display by department
    system.displayEmployeesByDepartment("IT");
    
    // Update operations
    cout << "\n--- Update Operations ---" << endl;
    system.updateEmployeeName(102, "Robert Smith");
    system.updateEmployeeDepartment(104, "Accounting");
    
    // Sorting operations
    cout << "\n--- Sorting Operations ---" << endl;
    system.sortByName();
    system.displayAll();
    
    system.sortById();
    system.displayAll();
    
    // Check out remaining employee and sort by hours
    system.checkOut(105);
    system.sortByHoursWorked();
    system.displayAll();
    
    // Display statistics
    system.displayStatistics();
    
    cout << "\n=== DEMO TEST COMPLETED ===" << endl;
}

int main() {
    EmployeeAttendanceSystem system;
    int choice;
    
    do {
        displayMenu();
        cin >> choice;
        
        switch (choice) {
            case 1: {
                int id;
                string name, department;
                cout << "Enter Employee ID: ";
                cin >> id;
                cin.ignore();
                cout << "Enter Employee Name: ";
                getline(cin, name);
                cout << "Enter Department: ";
                getline(cin, department);
                system.addEmployee(id, name, department);
                break;
            }
            case 2: {
                int id;
                cout << "Enter Employee ID to remove: ";
                cin >> id;
                system.removeEmployee(id);
                break;
            }
            case 3: {
                int id;
                cout << "Enter Employee ID to check in: ";
                cin >> id;
                system.checkIn(id);
                break;
            }
            case 4: {
                int id;
                cout << "Enter Employee ID to check out: ";
                cin >> id;
                system.checkOut(id);
                break;
            }
            case 5: {
                int id;
                cout << "Enter Employee ID to search: ";
                cin >> id;
                Node* found = system.searchById(id);
                if (found) {
                    cout << "Employee found: ";
                    found->data.displayInfo();
                } else {
                    cout << "Employee not found!" << endl;
                }
                break;
            }
            case 6: {
                string name;
                cout << "Enter Employee Name to search: ";
                cin.ignore();
                getline(cin, name);
                Node* found = system.searchByName(name);
                if (found) {
                    cout << "Employee found: ";
                    found->data.displayInfo();
                } else {
                    cout << "Employee not found!" << endl;
                }
                break;
            }
            case 7:
                system.displayAll();
                break;
            case 8:
                system.displayCheckedInEmployees();
                break;
            case 9:
                system.displayCheckedOutEmployees();
                break;
            case 10: {
                string department;
                cout << "Enter Department: ";
                cin.ignore();
                getline(cin, department);
                system.displayEmployeesByDepartment(department);
                break;
            }
            case 11: {
                int id;
                string newName;
                cout << "Enter Employee ID: ";
                cin >> id;
                cin.ignore();
                cout << "Enter New Name: ";
                getline(cin, newName);
                system.updateEmployeeName(id, newName);
                break;
            }
            case 12: {
                int id;
                string newDept;
                cout << "Enter Employee ID: ";
                cin >> id;
                cin.ignore();
                cout << "Enter New Department: ";
                getline(cin, newDept);
                system.updateEmployeeDepartment(id, newDept);
                break;
            }
            case 13:
                system.sortById();
                break;
            case 14:
                system.sortByName();
                break;
            case 15:
                system.sortByHoursWorked();
                break;
            case 16:
                system.displayStatistics();
                break;
            case 17:
                runDemoTest(system);
                break;
            case 0:
                cout << "Thank you for using Employee Attendance System!" << endl;
                break;
            default:
                cout << "Invalid choice! Please try again." << endl;
        }
    } while (choice != 0);
    
    return 0;
}
